package com.example.fakenewsdetect.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.fakenewsdetect.data.models.NewsAnalysis
import com.example.fakenewsdetect.navigation.Screen
import com.example.fakenewsdetect.ui.components.AppTopBar
import com.example.fakenewsdetect.ui.components.EmptyState
import com.example.fakenewsdetect.ui.components.LoadingIndicator
import com.example.fakenewsdetect.viewmodel.HistoryViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    navController: NavController,
    viewModel: HistoryViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Scaffold(
        topBar = {
            AppTopBar(
                title = "Analysis History",
                showBackButton = true,
                onBackClick = { navController.popBackStack() },
                actions = {
                    IconButton(onClick = { viewModel.showFilterDialog() }) {
                        Icon(Icons.Default.FilterList, contentDescription = "Filter")
                    }
                    IconButton(onClick = { viewModel.refreshHistory() }) {
                        Icon(Icons.Default.Refresh, contentDescription = "Refresh")
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { navController.navigate(Screen.NewsInput.route) }
            ) {
                Icon(Icons.Default.Add, contentDescription = "New Analysis")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Statistics Summary
            uiState.analysisHistory?.let { history ->
                HistoryStatsCard(
                    history = history,
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // Filter Indicator
            if (uiState.currentFilter != com.example.fakenewsdetect.data.models.HistoryFilter()) {
                FilterIndicatorCard(
                    onClearFilter = { viewModel.clearFilter() },
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // Content
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        message = "Loading history...",
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp)
                    )
                }
                
                uiState.filteredAnalyses.isEmpty() -> {
                    EmptyState(
                        title = if (uiState.currentFilter != com.example.fakenewsdetect.data.models.HistoryFilter()) {
                            "No results found"
                        } else {
                            "No analysis history"
                        },
                        description = if (uiState.currentFilter != com.example.fakenewsdetect.data.models.HistoryFilter()) {
                            "Try adjusting your filters or clear them to see all results"
                        } else {
                            "Start analyzing news to see your history here"
                        },
                        icon = Icons.Default.History,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp)
                    )
                }
                
                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(uiState.filteredAnalyses) { analysis ->
                            HistoryItem(
                                analysis = analysis,
                                onClick = {
                                    navController.navigate(Screen.AnalysisResult.createRoute(analysis.id))
                                },
                                onDelete = { viewModel.deleteAnalysis(analysis.id) }
                            )
                        }
                    }
                }
            }
        }
    }
    
    // Filter Dialog
    if (uiState.showFilterDialog) {
        FilterDialog(
            currentFilter = uiState.currentFilter,
            onApplyFilter = viewModel::applyFilter,
            onDismiss = { viewModel.hideFilterDialog() }
        )
    }
}

@Composable
private fun HistoryStatsCard(
    history: com.example.fakenewsdetect.data.models.AnalysisHistory,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(
                label = "Total",
                value = history.totalAnalyses.toString(),
                color = MaterialTheme.colorScheme.primary
            )
            StatItem(
                label = "Fake",
                value = history.fakeNewsDetected.toString(),
                color = Color(0xFFD32F2F)
            )
            StatItem(
                label = "Real",
                value = history.realNewsDetected.toString(),
                color = Color(0xFF388E3C)
            )
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun FilterIndicatorCard(
    onClearFilter: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.FilterList,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSecondaryContainer
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Filters applied",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                modifier = Modifier.weight(1f)
            )
            TextButton(onClick = onClearFilter) {
                Text("Clear")
            }
        }
    }
}

@Composable
private fun HistoryItem(
    analysis: NewsAnalysis,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = analysis.inputText.take(100) + if (analysis.inputText.length > 100) "..." else "",
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = if (analysis.isFake) Icons.Default.Warning else Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = if (analysis.isFake) "Fake" else "Real",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C),
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "${(analysis.confidenceScore * 100).toInt()}% confidence",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                IconButton(onClick = { showDeleteDialog = true }) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
            Text(
                text = dateFormat.format(analysis.analysisDate),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
    
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("Delete Analysis") },
            text = { Text("Are you sure you want to delete this analysis? This action cannot be undone.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteDialog = false
                    }
                ) {
                    Text("Delete", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun FilterDialog(
    currentFilter: com.example.fakenewsdetect.data.models.HistoryFilter,
    onApplyFilter: (com.example.fakenewsdetect.data.models.HistoryFilter) -> Unit,
    onDismiss: () -> Unit
) {
    var showFakeOnly by remember { mutableStateOf(currentFilter.showFakeOnly) }
    var showRealOnly by remember { mutableStateOf(currentFilter.showRealOnly) }
    var minConfidence by remember { mutableStateOf(currentFilter.minConfidence) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Filter History") },
        text = {
            Column {
                Text(
                    text = "Result Type",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FilterChip(
                        onClick = { 
                            showFakeOnly = !showFakeOnly
                            if (showFakeOnly) showRealOnly = false
                        },
                        label = { Text("Fake Only") },
                        selected = showFakeOnly
                    )
                    FilterChip(
                        onClick = { 
                            showRealOnly = !showRealOnly
                            if (showRealOnly) showFakeOnly = false
                        },
                        label = { Text("Real Only") },
                        selected = showRealOnly
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Minimum Confidence: ${(minConfidence * 100).toInt()}%",
                    style = MaterialTheme.typography.titleSmall
                )
                Slider(
                    value = minConfidence,
                    onValueChange = { minConfidence = it },
                    valueRange = 0f..1f
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onApplyFilter(
                        com.example.fakenewsdetect.data.models.HistoryFilter(
                            showFakeOnly = showFakeOnly,
                            showRealOnly = showRealOnly,
                            minConfidence = minConfidence
                        )
                    )
                }
            ) {
                Text("Apply")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
