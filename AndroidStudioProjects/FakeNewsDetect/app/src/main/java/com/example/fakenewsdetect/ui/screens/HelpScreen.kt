package com.example.fakenewsdetect.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.fakenewsdetect.ui.components.AppTopBar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HelpScreen(
    navController: NavController
) {
    Scaffold(
        topBar = {
            AppTopBar(
                title = "Help & FAQ",
                showBackButton = true,
                onBackClick = { navController.popBackStack() }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Quick Start Guide
            QuickStartSection()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // FAQ Section
            FAQSection()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Tips Section
            TipsSection()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Troubleshooting
            TroubleshootingSection()
        }
    }
}

@Composable
private fun QuickStartSection() {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(20.dp)) {
            Text(
                text = "Quick Start Guide",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val steps = listOf(
                Triple(Icons.Default.Add, "Start Analysis", "Tap the + button to begin analyzing news"),
                Triple(Icons.Default.TextFields, "Input Content", "Enter text or paste a URL to analyze"),
                Triple(Icons.Default.Search, "Analyze", "Tap 'Analyze News' to start the detection process"),
                Triple(Icons.Default.Assessment, "Review Results", "Check the confidence score and detailed analysis"),
                Triple(Icons.Default.History, "View History", "Access past analyses from the History tab")
            )
            
            steps.forEachIndexed { index, (icon, title, description) ->
                Row(
                    modifier = Modifier.padding(vertical = 8.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Surface(
                        modifier = Modifier.size(32.dp),
                        shape = MaterialTheme.shapes.small,
                        color = MaterialTheme.colorScheme.primaryContainer
                    ) {
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "${index + 1}",
                                style = MaterialTheme.typography.labelMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun FAQSection() {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(20.dp)) {
            Text(
                text = "Frequently Asked Questions",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val faqs = listOf(
                "How accurate is the fake news detection?" to "Our AI model provides confidence scores to help you make informed decisions. While highly accurate, it should be used alongside critical thinking and source verification.",
                
                "What types of content can I analyze?" to "You can analyze news articles, social media posts, blog content, and any text-based news content. Both direct text input and URL analysis are supported.",
                
                "Is my data stored or shared?" to "Your analysis data is stored locally on your device for history purposes. We do not share your personal data or analysis content with third parties.",
                
                "Why do I get different results for similar content?" to "Detection algorithms consider multiple factors including context, source, and subtle language patterns. Similar content may have different reliability indicators.",
                
                "Can I analyze content in different languages?" to "Currently, the app works best with English content. Support for additional languages may be added in future updates.",
                
                "How often is the detection model updated?" to "Our detection algorithms are continuously improved based on new patterns and user feedback to maintain accuracy against evolving misinformation tactics."
            )
            
            faqs.forEach { (question, answer) ->
                FAQItem(question = question, answer = answer)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun FAQItem(
    question: String,
    answer: String
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = { expanded = !expanded },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = question,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (expanded) "Collapse" else "Expand"
                )
            }
            
            AnimatedVisibility(visible = expanded) {
                Column {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = answer,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun TipsSection() {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(20.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Tips for Better Results",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val tips = listOf(
                "Include complete articles with headlines for more accurate analysis",
                "Longer text content generally provides better detection accuracy",
                "Check the source URL and publication date when available",
                "Compare results with multiple fact-checking sources",
                "Pay attention to confidence scores - lower scores suggest uncertainty",
                "Look for emotional language and sensational claims in the content",
                "Verify information through official sources and reputable news outlets"
            )
            
            tips.forEach { tip ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = tip,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun TroubleshootingSection() {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(20.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Build,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Troubleshooting",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val issues = listOf(
                "Analysis takes too long" to "Check your internet connection. Large texts may take longer to process.",
                "Low confidence scores" to "This may indicate ambiguous content. Try analyzing longer, more complete articles.",
                "App crashes during analysis" to "Restart the app and try with shorter text. Report persistent issues to support.",
                "History not saving" to "Check if auto-save is enabled in Settings. Ensure sufficient storage space.",
                "URL analysis not working" to "Verify the URL is accessible and contains readable text content."
            )
            
            issues.forEach { (problem, solution) ->
                Column(
                    modifier = Modifier.padding(vertical = 8.dp)
                ) {
                    Text(
                        text = "Problem: $problem",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.error
                    )
                    Text(
                        text = "Solution: $solution",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                if (issues.last() != (problem to solution)) {
                    Divider(modifier = Modifier.padding(vertical = 8.dp))
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Still need help?",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Contact our support <NAME_EMAIL>",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}
