package com.example.fakenewsdetect.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.fakenewsdetect.data.models.AnalysisHistory
import com.example.fakenewsdetect.data.repository.NewsRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class HomeUiState(
    val isLoading: Boolean = false,
    val analysisHistory: AnalysisHistory? = null,
    val quickStats: QuickStats = QuickStats()
)

data class QuickStats(
    val totalAnalyses: Int = 0,
    val fakeNewsDetected: Int = 0,
    val realNewsDetected: Int = 0,
    val averageConfidence: Float = 0f
)

class HomeViewModel : ViewModel() {
    private val repository = NewsRepository()
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadHomeData()
    }
    
    private fun loadHomeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val history = repository.getAnalysisHistory()
            val quickStats = calculateQuickStats(history)
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                analysisHistory = history,
                quickStats = quickStats
            )
        }
    }
    
    private fun calculateQuickStats(history: AnalysisHistory): QuickStats {
        val avgConfidence = if (history.analyses.isNotEmpty()) {
            history.analyses.map { it.confidenceScore }.average().toFloat()
        } else 0f
        
        return QuickStats(
            totalAnalyses = history.totalAnalyses,
            fakeNewsDetected = history.fakeNewsDetected,
            realNewsDetected = history.realNewsDetected,
            averageConfidence = avgConfidence
        )
    }
    
    fun refreshData() {
        loadHomeData()
    }
}
