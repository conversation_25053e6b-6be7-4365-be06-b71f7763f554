package com.example.fakenewsdetect.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.fakenewsdetect.ui.screens.*

sealed class Screen(val route: String) {
    object Home : Screen("home")
    object NewsInput : Screen("news_input")
    object AnalysisResult : Screen("analysis_result/{analysisId}") {
        fun createRoute(analysisId: String) = "analysis_result/$analysisId"
    }
    object History : Screen("history")
    object Settings : Screen("settings")
    object About : Screen("about")
    object Help : Screen("help")
}

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Home.route
    ) {
        composable(Screen.Home.route) {
            HomeScreen(navController = navController)
        }
        
        composable(Screen.NewsInput.route) {
            NewsInputScreen(navController = navController)
        }
        
        composable(Screen.AnalysisResult.route) { backStackEntry ->
            val analysisId = backStackEntry.arguments?.getString("analysisId") ?: ""
            AnalysisResultScreen(
                navController = navController,
                analysisId = analysisId
            )
        }
        
        composable(Screen.History.route) {
            HistoryScreen(navController = navController)
        }
        
        composable(Screen.Settings.route) {
            SettingsScreen(navController = navController)
        }
        
        composable(Screen.About.route) {
            AboutScreen(navController = navController)
        }
        
        composable(Screen.Help.route) {
            HelpScreen(navController = navController)
        }
    }
}
