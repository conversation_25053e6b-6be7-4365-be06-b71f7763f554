package com.example.fakenewsdetect.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.fakenewsdetect.navigation.Screen
import com.example.fakenewsdetect.ui.components.AppTopBar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController
) {
    var notificationsEnabled by remember { mutableStateOf(true) }
    var autoSaveHistory by remember { mutableStateOf(true) }
    var showConfidenceDetails by remember { mutableStateOf(true) }
    var darkModeEnabled by remember { mutableStateOf(false) }
    var showClearHistoryDialog by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            AppTopBar(
                title = "Settings",
                showBackButton = true,
                onBackClick = { navController.popBackStack() }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Analysis Settings
            SettingsSection(title = "Analysis Settings") {
                SettingsItem(
                    title = "Show Confidence Details",
                    description = "Display detailed confidence breakdown in results",
                    icon = Icons.Default.Analytics,
                    trailing = {
                        Switch(
                            checked = showConfidenceDetails,
                            onCheckedChange = { showConfidenceDetails = it }
                        )
                    }
                )
                
                SettingsItem(
                    title = "Auto-save History",
                    description = "Automatically save analysis results to history",
                    icon = Icons.Default.Save,
                    trailing = {
                        Switch(
                            checked = autoSaveHistory,
                            onCheckedChange = { autoSaveHistory = it }
                        )
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Notification Settings
            SettingsSection(title = "Notifications") {
                SettingsItem(
                    title = "Enable Notifications",
                    description = "Receive notifications about analysis completion",
                    icon = Icons.Default.Notifications,
                    trailing = {
                        Switch(
                            checked = notificationsEnabled,
                            onCheckedChange = { notificationsEnabled = it }
                        )
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Appearance Settings
            SettingsSection(title = "Appearance") {
                SettingsItem(
                    title = "Dark Mode",
                    description = "Use dark theme throughout the app",
                    icon = Icons.Default.DarkMode,
                    trailing = {
                        Switch(
                            checked = darkModeEnabled,
                            onCheckedChange = { darkModeEnabled = it }
                        )
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Data Management
            SettingsSection(title = "Data Management") {
                SettingsItem(
                    title = "Clear History",
                    description = "Remove all saved analysis history",
                    icon = Icons.Default.DeleteSweep,
                    onClick = { showClearHistoryDialog = true }
                )
                
                SettingsItem(
                    title = "Export Data",
                    description = "Export your analysis history",
                    icon = Icons.Default.FileDownload,
                    onClick = { /* TODO: Implement export */ }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Information
            SettingsSection(title = "Information") {
                SettingsItem(
                    title = "About",
                    description = "Learn more about this app",
                    icon = Icons.Default.Info,
                    onClick = { navController.navigate(Screen.About.route) }
                )
                
                SettingsItem(
                    title = "Help & FAQ",
                    description = "Get help and find answers",
                    icon = Icons.Default.Help,
                    onClick = { navController.navigate(Screen.Help.route) }
                )
                
                SettingsItem(
                    title = "Privacy Policy",
                    description = "Read our privacy policy",
                    icon = Icons.Default.PrivacyTip,
                    onClick = { /* TODO: Open privacy policy */ }
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // App Version
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Fake News Detector",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Version 1.0.0",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Built with ❤️ for fighting misinformation",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
    
    // Clear History Dialog
    if (showClearHistoryDialog) {
        AlertDialog(
            onDismissRequest = { showClearHistoryDialog = false },
            title = { Text("Clear History") },
            text = { 
                Text("Are you sure you want to clear all analysis history? This action cannot be undone.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        // TODO: Implement clear history
                        showClearHistoryDialog = false
                    }
                ) {
                    Text("Clear", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearHistoryDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun SettingsSection(
    title: String,
    content: @Composable () -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        Card(modifier = Modifier.fillMaxWidth()) {
            Column {
                content()
            }
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    trailing: @Composable (() -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    val modifier = if (onClick != null) {
        Modifier.fillMaxWidth()
    } else {
        Modifier.fillMaxWidth()
    }
    
    Surface(
        modifier = modifier,
        onClick = onClick ?: {}
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            trailing?.let {
                Spacer(modifier = Modifier.width(16.dp))
                it()
            } ?: run {
                if (onClick != null) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}
