package com.example.fakenewsdetect.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.fakenewsdetect.navigation.Screen
import com.example.fakenewsdetect.ui.components.*
import com.example.fakenewsdetect.viewmodel.HomeViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    navController: NavController,
    viewModel: HomeViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Scaffold(
        topBar = {
            AppTopBar(
                title = "Fake News Detector",
                actions = {
                    IconButton(onClick = { navController.navigate(Screen.Settings.route) }) {
                        Icon(Icons.Default.Settings, contentDescription = "Settings")
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { navController.navigate(Screen.NewsInput.route) },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(Icons.Default.Add, contentDescription = "Analyze News")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Welcome Section
            WelcomeSection()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Quick Stats
            if (uiState.isLoading) {
                LoadingIndicator(
                    message = "Loading statistics...",
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                QuickStatsSection(uiState.quickStats)
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Quick Actions
            QuickActionsSection(navController)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Recent Analysis (if any)
            uiState.analysisHistory?.let { history ->
                if (history.analyses.isNotEmpty()) {
                    RecentAnalysisSection(
                        recentAnalyses = history.analyses.take(3),
                        onViewAll = { navController.navigate(Screen.History.route) },
                        onAnalysisClick = { analysis ->
                            navController.navigate(Screen.AnalysisResult.createRoute(analysis.id))
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun WelcomeSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "Welcome to Fake News Detector",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Analyze news articles and text to detect potential misinformation using advanced AI algorithms.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
            )
        }
    }
}

@Composable
private fun QuickStatsSection(quickStats: com.example.fakenewsdetect.viewmodel.QuickStats) {
    Text(
        text = "Quick Statistics",
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 12.dp)
    )
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            listOf(
                Triple("Total Analyses", quickStats.totalAnalyses.toString(), Icons.Default.Analytics),
                Triple("Fake Detected", quickStats.fakeNewsDetected.toString(), Icons.Default.Warning),
                Triple("Real News", quickStats.realNewsDetected.toString(), Icons.Default.CheckCircle),
                Triple("Avg Confidence", "${(quickStats.averageConfidence * 100).toInt()}%", Icons.Default.TrendingUp)
            )
        ) { (title, value, icon) ->
            StatCard(
                title = title,
                value = value,
                icon = icon,
                backgroundColor = when (title) {
                    "Fake Detected" -> Color(0xFFFFEBEE)
                    "Real News" -> Color(0xFFE8F5E8)
                    else -> MaterialTheme.colorScheme.surfaceVariant
                },
                contentColor = when (title) {
                    "Fake Detected" -> Color(0xFFD32F2F)
                    "Real News" -> Color(0xFF388E3C)
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        }
    }
}

@Composable
private fun QuickActionsSection(navController: NavController) {
    Text(
        text = "Quick Actions",
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 12.dp)
    )
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        QuickActionCard(
            title = "Analyze Text",
            description = "Check news text",
            icon = Icons.Default.TextFields,
            modifier = Modifier.weight(1f),
            onClick = { navController.navigate(Screen.NewsInput.route) }
        )
        
        QuickActionCard(
            title = "View History",
            description = "Past analyses",
            icon = Icons.Default.History,
            modifier = Modifier.weight(1f),
            onClick = { navController.navigate(Screen.History.route) }
        )
    }
}

@Composable
private fun QuickActionCard(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        modifier = modifier,
        onClick = onClick
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun RecentAnalysisSection(
    recentAnalyses: List<com.example.fakenewsdetect.data.models.NewsAnalysis>,
    onViewAll: () -> Unit,
    onAnalysisClick: (com.example.fakenewsdetect.data.models.NewsAnalysis) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Recent Analysis",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        TextButton(onClick = onViewAll) {
            Text("View All")
        }
    }
    
    Spacer(modifier = Modifier.height(8.dp))
    
    recentAnalyses.forEach { analysis ->
        RecentAnalysisItem(
            analysis = analysis,
            onClick = { onAnalysisClick(analysis) }
        )
        Spacer(modifier = Modifier.height(8.dp))
    }
}

@Composable
private fun RecentAnalysisItem(
    analysis: com.example.fakenewsdetect.data.models.NewsAnalysis,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (analysis.isFake) Icons.Default.Warning else Icons.Default.CheckCircle,
                contentDescription = null,
                tint = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = analysis.inputText.take(50) + if (analysis.inputText.length > 50) "..." else "",
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = if (analysis.isFake) "Likely Fake" else "Likely Real",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C)
                )
            }
            Text(
                text = "${(analysis.confidenceScore * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
        }
    }
}
