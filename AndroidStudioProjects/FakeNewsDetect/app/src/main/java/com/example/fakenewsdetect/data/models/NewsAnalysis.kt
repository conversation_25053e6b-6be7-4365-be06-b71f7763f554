package com.example.fakenewsdetect.data.models

import java.util.Date

data class NewsAnalysis(
    val id: String,
    val inputText: String,
    val inputUrl: String? = null,
    val isFake: Boolean,
    val confidenceScore: Float, // 0.0 to 1.0
    val analysisDate: Date,
    val analysisDetails: AnalysisDetails
)

data class AnalysisDetails(
    val credibilityScore: Float,
    val biasScore: Float,
    val emotionalToneScore: Float,
    val factualityScore: Float,
    val sourceReliability: SourceReliability,
    val keyIndicators: List<String>,
    val warnings: List<String>
)

data class SourceReliability(
    val domainTrust: Float,
    val authorCredibility: Float,
    val publicationDate: String?,
    val sourceType: SourceType
)

enum class SourceType {
    NEWS_WEBSITE,
    SOCIAL_MEDIA,
    BLOG,
    ACADEMIC,
    GOVERNMENT,
    UNKNOWN
}

enum class AnalysisResult {
    REAL,
    FAKE,
    UNCERTAIN
}
