package com.example.fakenewsdetect.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.fakenewsdetect.navigation.Screen
import com.example.fakenewsdetect.ui.components.AppTopBar
import com.example.fakenewsdetect.ui.components.LoadingIndicator
import com.example.fakenewsdetect.viewmodel.InputMode
import com.example.fakenewsdetect.viewmodel.NewsInputViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewsInputScreen(
    navController: NavController,
    viewModel: NewsInputViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    // Navigate to results when analysis is complete
    LaunchedEffect(uiState.analysisResult) {
        uiState.analysisResult?.let { analysis ->
            navController.navigate(Screen.AnalysisResult.createRoute(analysis.id)) {
                popUpTo(Screen.NewsInput.route) { inclusive = true }
            }
        }
    }
    
    Scaffold(
        topBar = {
            AppTopBar(
                title = "Analyze News",
                showBackButton = true,
                onBackClick = { navController.popBackStack() },
                actions = {
                    IconButton(onClick = { viewModel.clearInput() }) {
                        Icon(Icons.Default.Clear, contentDescription = "Clear")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp)
        ) {
            // Input Mode Selection
            InputModeSelector(
                selectedMode = uiState.inputMode,
                onModeSelected = viewModel::setInputMode
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Input Fields
            when (uiState.inputMode) {
                InputMode.TEXT -> {
                    TextInputSection(
                        text = uiState.newsText,
                        onTextChange = viewModel::updateNewsText,
                        isAnalyzing = uiState.isAnalyzing
                    )
                }
                InputMode.URL -> {
                    UrlInputSection(
                        url = uiState.newsUrl,
                        onUrlChange = viewModel::updateNewsUrl,
                        isAnalyzing = uiState.isAnalyzing
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Error Display
            uiState.error?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.weight(1f)
                        )
                        IconButton(onClick = viewModel::clearError) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Dismiss",
                                tint = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Analyze Button or Loading
            if (uiState.isAnalyzing) {
                LoadingIndicator(
                    message = "Analyzing news content...",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp)
                )
            } else {
                Button(
                    onClick = viewModel::analyzeNews,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !uiState.isAnalyzing
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Analyze News",
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Tips Section
            TipsSection()
        }
    }
}

@Composable
private fun InputModeSelector(
    selectedMode: InputMode,
    onModeSelected: (InputMode) -> Unit
) {
    Text(
        text = "Input Method",
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 12.dp)
    )
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        FilterChip(
            onClick = { onModeSelected(InputMode.TEXT) },
            label = { Text("Text Input") },
            selected = selectedMode == InputMode.TEXT,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.TextFields,
                    contentDescription = null
                )
            },
            modifier = Modifier.weight(1f)
        )
        
        FilterChip(
            onClick = { onModeSelected(InputMode.URL) },
            label = { Text("URL Input") },
            selected = selectedMode == InputMode.URL,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Link,
                    contentDescription = null
                )
            },
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun TextInputSection(
    text: String,
    onTextChange: (String) -> Unit,
    isAnalyzing: Boolean
) {
    Text(
        text = "Enter News Text",
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 8.dp)
    )
    
    OutlinedTextField(
        value = text,
        onValueChange = onTextChange,
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        placeholder = { Text("Paste the news article or text you want to analyze...") },
        enabled = !isAnalyzing,
        supportingText = {
            Text("${text.length} characters • Minimum 10 characters required")
        }
    )
}

@Composable
private fun UrlInputSection(
    url: String,
    onUrlChange: (String) -> Unit,
    isAnalyzing: Boolean
) {
    Text(
        text = "Enter News URL",
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(bottom = 8.dp)
    )
    
    OutlinedTextField(
        value = url,
        onValueChange = onUrlChange,
        modifier = Modifier.fillMaxWidth(),
        placeholder = { Text("https://example.com/news-article") },
        enabled = !isAnalyzing,
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Link,
                contentDescription = null
            )
        },
        supportingText = {
            Text("Enter a valid URL to a news article")
        }
    )
}

@Composable
private fun TipsSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Tips for Better Analysis",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            val tips = listOf(
                "Provide complete articles for more accurate results",
                "Include headlines and main content",
                "Longer texts generally yield better analysis",
                "Check multiple sources for verification"
            )
            
            tips.forEach { tip ->
                Row(
                    modifier = Modifier.padding(vertical = 2.dp)
                ) {
                    Text(
                        text = "• ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = tip,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}
