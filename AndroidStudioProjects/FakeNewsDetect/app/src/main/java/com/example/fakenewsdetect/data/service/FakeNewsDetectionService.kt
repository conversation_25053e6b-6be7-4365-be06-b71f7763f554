package com.example.fakenewsdetect.data.service

import com.example.fakenewsdetect.data.models.*
import kotlinx.coroutines.delay
import java.util.*
import kotlin.random.Random

class FakeNewsDetectionService {
    
    suspend fun analyzeNews(text: String, url: String? = null): NewsAnalysis {
        // Simulate API call delay
        delay(2000)
        
        // Mock analysis logic
        val isFake = detectFakeNews(text)
        val confidence = generateConfidenceScore(text)
        val details = generateAnalysisDetails(text, url)
        
        return NewsAnalysis(
            id = UUID.randomUUID().toString(),
            inputText = text,
            inputUrl = url,
            isFake = isFake,
            confidenceScore = confidence,
            analysisDate = Date(),
            analysisDetails = details
        )
    }
    
    private fun detectFakeNews(text: String): Boolean {
        // Mock detection logic based on keywords and patterns
        val fakeIndicators = listOf(
            "breaking", "shocking", "unbelievable", "you won't believe",
            "doctors hate this", "secret", "exposed", "leaked",
            "urgent", "must read", "viral", "exclusive"
        )
        
        val lowerText = text.lowercase()
        val fakeKeywordCount = fakeIndicators.count { lowerText.contains(it) }
        
        // Simple heuristic: if text has many fake indicators or is very short/long
        return when {
            fakeKeywordCount >= 3 -> true
            text.length < 50 -> Random.nextBoolean()
            text.length > 5000 -> Random.nextFloat() > 0.7f
            lowerText.contains("fake") -> true
            else -> Random.nextFloat() > 0.6f // 40% chance of being fake
        }
    }
    
    private fun generateConfidenceScore(text: String): Float {
        // Generate confidence based on text characteristics
        return when {
            text.length < 100 -> Random.nextFloat() * 0.4f + 0.3f // Low confidence for short text
            text.length > 1000 -> Random.nextFloat() * 0.3f + 0.7f // High confidence for long text
            else -> Random.nextFloat() * 0.6f + 0.4f // Medium confidence
        }
    }
    
    private fun generateAnalysisDetails(text: String, url: String?): AnalysisDetails {
        val sourceType = determineSourceType(url)
        val keyIndicators = generateKeyIndicators(text)
        val warnings = generateWarnings(text)
        
        return AnalysisDetails(
            credibilityScore = Random.nextFloat(),
            biasScore = Random.nextFloat(),
            emotionalToneScore = Random.nextFloat(),
            factualityScore = Random.nextFloat(),
            sourceReliability = SourceReliability(
                domainTrust = Random.nextFloat(),
                authorCredibility = Random.nextFloat(),
                publicationDate = if (Random.nextBoolean()) "2024-01-15" else null,
                sourceType = sourceType
            ),
            keyIndicators = keyIndicators,
            warnings = warnings
        )
    }
    
    private fun determineSourceType(url: String?): SourceType {
        return when {
            url == null -> SourceType.UNKNOWN
            url.contains("twitter.com") || url.contains("facebook.com") -> SourceType.SOCIAL_MEDIA
            url.contains("blog") -> SourceType.BLOG
            url.contains(".edu") -> SourceType.ACADEMIC
            url.contains(".gov") -> SourceType.GOVERNMENT
            else -> SourceType.NEWS_WEBSITE
        }
    }
    
    private fun generateKeyIndicators(text: String): List<String> {
        val indicators = mutableListOf<String>()
        val lowerText = text.lowercase()
        
        if (lowerText.contains("breaking")) indicators.add("Sensational language detected")
        if (lowerText.contains("exclusive")) indicators.add("Exclusive claims without verification")
        if (text.count { it == '!' } > 5) indicators.add("Excessive exclamation marks")
        if (lowerText.contains("you won't believe")) indicators.add("Clickbait patterns detected")
        if (text.length < 100) indicators.add("Unusually short content")
        
        return indicators.ifEmpty { listOf("Standard content patterns") }
    }
    
    private fun generateWarnings(text: String): List<String> {
        val warnings = mutableListOf<String>()
        
        if (text.length < 50) warnings.add("Content too short for reliable analysis")
        if (!text.contains(".") && text.length > 100) warnings.add("Lack of proper sentence structure")
        if (text.count { it.isUpperCase() } > text.length * 0.3) warnings.add("Excessive use of capital letters")
        
        return warnings
    }
}
