package com.example.fakenewsdetect.data.repository

import com.example.fakenewsdetect.data.models.*
import com.example.fakenewsdetect.data.service.FakeNewsDetectionService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class NewsRepository {
    private val detectionService = FakeNewsDetectionService()
    private val _analysisHistory = MutableStateFlow<List<NewsAnalysis>>(emptyList())
    val analysisHistory: StateFlow<List<NewsAnalysis>> = _analysisHistory.asStateFlow()
    
    suspend fun analyzeNews(text: String, url: String? = null): NewsAnalysis {
        val analysis = detectionService.analyzeNews(text, url)
        
        // Add to history
        val currentHistory = _analysisHistory.value.toMutableList()
        currentHistory.add(0, analysis) // Add to beginning
        _analysisHistory.value = currentHistory
        
        return analysis
    }
    
    fun getAnalysisHistory(): AnalysisHistory {
        val analyses = _analysisHistory.value
        val fakeCount = analyses.count { it.isFake }
        val realCount = analyses.count { !it.isFake }
        
        return AnalysisHistory(
            analyses = analyses,
            totalAnalyses = analyses.size,
            fakeNewsDetected = fakeCount,
            realNewsDetected = realCount
        )
    }
    
    fun getFilteredHistory(filter: HistoryFilter): List<NewsAnalysis> {
        var filtered = _analysisHistory.value
        
        if (filter.showFakeOnly) {
            filtered = filtered.filter { it.isFake }
        } else if (filter.showRealOnly) {
            filtered = filtered.filter { !it.isFake }
        }
        
        if (filter.minConfidence > 0) {
            filtered = filtered.filter { it.confidenceScore >= filter.minConfidence }
        }
        
        filter.dateRange?.let { range ->
            filtered = filtered.filter { analysis ->
                analysis.analysisDate.after(range.startDate) && 
                analysis.analysisDate.before(range.endDate)
            }
        }
        
        return filtered
    }
    
    fun getAnalysisById(id: String): NewsAnalysis? {
        return _analysisHistory.value.find { it.id == id }
    }
    
    fun clearHistory() {
        _analysisHistory.value = emptyList()
    }
    
    fun deleteAnalysis(id: String) {
        val currentHistory = _analysisHistory.value.toMutableList()
        currentHistory.removeAll { it.id == id }
        _analysisHistory.value = currentHistory
    }
}
