package com.example.fakenewsdetect.data.models

data class AnalysisHistory(
    val analyses: List<NewsAnalysis>,
    val totalAnalyses: Int,
    val fakeNewsDetected: Int,
    val realNewsDetected: Int
) {
    val fakeNewsPercentage: Float
        get() = if (totalAnalyses > 0) (fakeNewsDetected.toFloat() / totalAnalyses) * 100 else 0f
    
    val realNewsPercentage: Float
        get() = if (totalAnalyses > 0) (realNewsDetected.toFloat() / totalAnalyses) * 100 else 0f
}

data class HistoryFilter(
    val showFakeOnly: Boolean = false,
    val showRealOnly: Boolean = false,
    val dateRange: DateRange? = null,
    val minConfidence: Float = 0f
)

data class DateRange(
    val startDate: java.util.Date,
    val endDate: java.util.Date
)
