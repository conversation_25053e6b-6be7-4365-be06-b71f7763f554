package com.example.fakenewsdetect.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.fakenewsdetect.data.models.AnalysisHistory
import com.example.fakenewsdetect.data.models.HistoryFilter
import com.example.fakenewsdetect.data.models.NewsAnalysis
import com.example.fakenewsdetect.data.repository.NewsRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class HistoryUiState(
    val isLoading: Boolean = false,
    val analysisHistory: AnalysisHistory? = null,
    val filteredAnalyses: List<NewsAnalysis> = emptyList(),
    val currentFilter: HistoryFilter = HistoryFilter(),
    val showFilterDialog: Boolean = false,
    val selectedAnalysis: NewsAnalysis? = null
)

class HistoryViewModel : ViewModel() {
    private val repository = NewsRepository()
    
    private val _uiState = MutableStateFlow(HistoryUiState())
    val uiState: StateFlow<HistoryUiState> = _uiState.asStateFlow()
    
    init {
        loadHistory()
    }
    
    private fun loadHistory() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val history = repository.getAnalysisHistory()
            val filtered = repository.getFilteredHistory(_uiState.value.currentFilter)
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                analysisHistory = history,
                filteredAnalyses = filtered
            )
        }
    }
    
    fun applyFilter(filter: HistoryFilter) {
        viewModelScope.launch {
            val filtered = repository.getFilteredHistory(filter)
            _uiState.value = _uiState.value.copy(
                currentFilter = filter,
                filteredAnalyses = filtered,
                showFilterDialog = false
            )
        }
    }
    
    fun clearFilter() {
        applyFilter(HistoryFilter())
    }
    
    fun showFilterDialog() {
        _uiState.value = _uiState.value.copy(showFilterDialog = true)
    }
    
    fun hideFilterDialog() {
        _uiState.value = _uiState.value.copy(showFilterDialog = false)
    }
    
    fun selectAnalysis(analysis: NewsAnalysis) {
        _uiState.value = _uiState.value.copy(selectedAnalysis = analysis)
    }
    
    fun clearSelection() {
        _uiState.value = _uiState.value.copy(selectedAnalysis = null)
    }
    
    fun deleteAnalysis(analysisId: String) {
        viewModelScope.launch {
            repository.deleteAnalysis(analysisId)
            loadHistory() // Refresh the list
        }
    }
    
    fun clearAllHistory() {
        viewModelScope.launch {
            repository.clearHistory()
            loadHistory()
        }
    }
    
    fun refreshHistory() {
        loadHistory()
    }
}
