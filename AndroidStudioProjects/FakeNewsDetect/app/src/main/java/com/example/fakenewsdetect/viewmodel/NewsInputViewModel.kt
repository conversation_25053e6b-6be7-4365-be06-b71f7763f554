package com.example.fakenewsdetect.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.fakenewsdetect.data.models.NewsAnalysis
import com.example.fakenewsdetect.data.repository.NewsRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class NewsInputUiState(
    val newsText: String = "",
    val newsUrl: String = "",
    val isAnalyzing: Boolean = false,
    val analysisResult: NewsAnalysis? = null,
    val error: String? = null,
    val inputMode: InputMode = InputMode.TEXT
)

enum class InputMode {
    TEXT, URL
}

class NewsInputViewModel : ViewModel() {
    private val repository = NewsRepository()
    
    private val _uiState = MutableStateFlow(NewsInputUiState())
    val uiState: StateFlow<NewsInputUiState> = _uiState.asStateFlow()
    
    fun updateNewsText(text: String) {
        _uiState.value = _uiState.value.copy(newsText = text, error = null)
    }
    
    fun updateNewsUrl(url: String) {
        _uiState.value = _uiState.value.copy(newsUrl = url, error = null)
    }
    
    fun setInputMode(mode: InputMode) {
        _uiState.value = _uiState.value.copy(inputMode = mode, error = null)
    }
    
    fun analyzeNews() {
        val currentState = _uiState.value
        
        // Validation
        when (currentState.inputMode) {
            InputMode.TEXT -> {
                if (currentState.newsText.isBlank()) {
                    _uiState.value = currentState.copy(error = "Please enter some news text to analyze")
                    return
                }
                if (currentState.newsText.length < 10) {
                    _uiState.value = currentState.copy(error = "News text is too short for reliable analysis")
                    return
                }
            }
            InputMode.URL -> {
                if (currentState.newsUrl.isBlank()) {
                    _uiState.value = currentState.copy(error = "Please enter a news URL to analyze")
                    return
                }
                if (!isValidUrl(currentState.newsUrl)) {
                    _uiState.value = currentState.copy(error = "Please enter a valid URL")
                    return
                }
            }
        }
        
        viewModelScope.launch {
            _uiState.value = currentState.copy(isAnalyzing = true, error = null)
            
            try {
                val analysis = when (currentState.inputMode) {
                    InputMode.TEXT -> repository.analyzeNews(currentState.newsText)
                    InputMode.URL -> repository.analyzeNews(
                        text = "Content from: ${currentState.newsUrl}",
                        url = currentState.newsUrl
                    )
                }
                
                _uiState.value = _uiState.value.copy(
                    isAnalyzing = false,
                    analysisResult = analysis
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isAnalyzing = false,
                    error = "Analysis failed: ${e.message}"
                )
            }
        }
    }
    
    fun clearInput() {
        _uiState.value = NewsInputUiState()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = Regex(
                "^(https?://)?" +
                "([\\w\\-]+\\.)+[\\w\\-]+" +
                "(/[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=]*)?$"
            )
            urlPattern.matches(url)
        } catch (e: Exception) {
            false
        }
    }
}
