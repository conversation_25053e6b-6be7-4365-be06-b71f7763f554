package com.example.fakenewsdetect.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.fakenewsdetect.data.models.NewsAnalysis
import com.example.fakenewsdetect.data.repository.NewsRepository
import com.example.fakenewsdetect.navigation.Screen
import com.example.fakenewsdetect.ui.components.AppTopBar
import com.example.fakenewsdetect.ui.components.ConfidenceBar
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnalysisResultScreen(
    navController: NavController,
    analysisId: String
) {
    val repository = remember { NewsRepository() }
    var analysis by remember { mutableStateOf<NewsAnalysis?>(null) }
    
    LaunchedEffect(analysisId) {
        analysis = repository.getAnalysisById(analysisId)
    }
    
    analysis?.let { analysisData ->
        Scaffold(
            topBar = {
                AppTopBar(
                    title = "Analysis Result",
                    showBackButton = true,
                    onBackClick = { navController.popBackStack() },
                    actions = {
                        IconButton(onClick = { 
                            navController.navigate(Screen.NewsInput.route)
                        }) {
                            Icon(Icons.Default.Add, contentDescription = "New Analysis")
                        }
                    }
                )
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp)
            ) {
                // Main Result Card
                MainResultCard(analysisData)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Confidence Score
                ConfidenceSection(analysisData)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Detailed Analysis
                DetailedAnalysisSection(analysisData)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Key Indicators
                KeyIndicatorsSection(analysisData)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Warnings (if any)
                if (analysisData.analysisDetails.warnings.isNotEmpty()) {
                    WarningsSection(analysisData)
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // Source Information
                SourceInformationSection(analysisData)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Original Text
                OriginalTextSection(analysisData)
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Action Buttons
                ActionButtonsSection(navController)
            }
        }
    } ?: run {
        // Loading or error state
        Scaffold(
            topBar = {
                AppTopBar(
                    title = "Analysis Result",
                    showBackButton = true,
                    onBackClick = { navController.popBackStack() }
                )
            }
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Analysis not found",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    TextButton(onClick = { navController.popBackStack() }) {
                        Text("Go Back")
                    }
                }
            }
        }
    }
}

@Composable
private fun MainResultCard(analysis: NewsAnalysis) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (analysis.isFake) {
                Color(0xFFFFEBEE)
            } else {
                Color(0xFFE8F5E8)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = if (analysis.isFake) Icons.Default.Warning else Icons.Default.CheckCircle,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = if (analysis.isFake) "Likely FAKE News" else "Likely REAL News",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = if (analysis.isFake) Color(0xFFD32F2F) else Color(0xFF388E3C)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val dateFormat = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
            Text(
                text = "Analyzed on ${dateFormat.format(analysis.analysisDate)}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ConfidenceSection(analysis: NewsAnalysis) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Confidence Score",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(12.dp))
            ConfidenceBar(confidence = analysis.confidenceScore)
        }
    }
}

@Composable
private fun DetailedAnalysisSection(analysis: NewsAnalysis) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Detailed Analysis",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            val details = analysis.analysisDetails
            val metrics = listOf(
                "Credibility" to details.credibilityScore,
                "Bias Level" to details.biasScore,
                "Emotional Tone" to details.emotionalToneScore,
                "Factuality" to details.factualityScore
            )
            
            metrics.forEach { (label, score) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    
                    Box(
                        modifier = Modifier
                            .width(100.dp)
                            .height(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(MaterialTheme.colorScheme.surfaceVariant)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .fillMaxWidth(score)
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    when {
                                        score >= 0.7f -> Color(0xFF4CAF50)
                                        score >= 0.4f -> Color(0xFFFF9800)
                                        else -> Color(0xFFF44336)
                                    }
                                )
                        )
                    }
                    
                    Text(
                        text = "${(score * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.width(40.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun KeyIndicatorsSection(analysis: NewsAnalysis) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Key Indicators",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            analysis.analysisDetails.keyIndicators.forEach { indicator ->
                Row(
                    modifier = Modifier.padding(vertical = 2.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.FiberManualRecord,
                        contentDescription = null,
                        modifier = Modifier.size(8.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = indicator,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun WarningsSection(analysis: NewsAnalysis) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Warnings",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.error
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            analysis.analysisDetails.warnings.forEach { warning ->
                Text(
                    text = "• $warning",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

@Composable
private fun SourceInformationSection(analysis: NewsAnalysis) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Source Information",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            val sourceInfo = analysis.analysisDetails.sourceReliability
            
            if (analysis.inputUrl != null) {
                Text(
                    text = "URL: ${analysis.inputUrl}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            Text(
                text = "Source Type: ${sourceInfo.sourceType.name.replace('_', ' ').lowercase().replaceFirstChar { it.uppercase() }}",
                style = MaterialTheme.typography.bodyMedium
            )
            
            sourceInfo.publicationDate?.let { date ->
                Text(
                    text = "Publication Date: $date",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun OriginalTextSection(analysis: NewsAnalysis) {
    Card(modifier = Modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "Original Text",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Text(
                text = analysis.inputText,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ActionButtonsSection(navController: NavController) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        OutlinedButton(
            onClick = { navController.navigate(Screen.NewsInput.route) },
            modifier = Modifier.weight(1f)
        ) {
            Icon(Icons.Default.Add, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("New Analysis")
        }
        
        Button(
            onClick = { navController.navigate(Screen.History.route) },
            modifier = Modifier.weight(1f)
        ) {
            Icon(Icons.Default.History, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("View History")
        }
    }
}
